import serial
import serial.tools.list_ports
import struct
import time

def send_lidar_command(ser, command):
    """Pošli príkaz LiDARu"""
    ser.write(command)
    time.sleep(0.1)

def find_lidar_port():
    """Nájdi port s YDLIDAR"""
    print("🔍 Hľadám dostupné sériové porty...")

    # Zoznam všetkých portov
    ports = serial.tools.list_ports.comports()

    if not ports:
        print("❌ Nenašiel som žiadne sériové porty!")
        return None

    print(f"📋 Našiel som {len(ports)} portov:")
    for port in ports:
        print(f"   {port.device} - {port.description}")

    # Testuj každý port
    for port in ports:
        print(f"\n🔌 Testovanie portu {port.device}...")

        for baudrate in [128000, 115200, 230400, 512000]:
            try:
                print(f"   🔍 Baudrate: {baudrate}")
                ser = serial.Serial(port.device, baudrate, timeout=2)
                time.sleep(1)

                # <PERSON>y<PERSON><PERSON><PERSON> buffer
                ser.reset_input_buffer()

                # Pošli príkazy na spustenie skeningu (podľa YDLIDAR protokolu)
                print("   📤 Posielam príkazy na spustenie skeningu...")

                # Príkaz na spustenie skeningu: 0xA5 0x60
                send_lidar_command(ser, b'\xA5\x60')

                # Príkaz na nastavenie rýchlosti: 0xA5 0xF0 0x02 0x94 0x02
                send_lidar_command(ser, b'\xA5\xF0\x02\x94\x02')

                time.sleep(2)  # Počkaj na spustenie

                # Prečítaj dáta
                data = b''
                for attempt in range(3):
                    chunk = ser.read(200)
                    data += chunk
                    if len(chunk) > 0:
                        print(f"   📥 Pokus {attempt + 1}: {len(chunk)} bytov")
                    time.sleep(0.5)

                if len(data) > 0:
                    print(f"   ✅ Prijal som {len(data)} bytov")
                    
                    # Hľadaj hlavičku YDLIDAR
                    for i in range(len(data) - 1):
                        if data[i] == 0xAA and data[i+1] == 0x55:
                            print(f"   🎯 Našiel som YDLIDAR hlavičku!")
                            
                            # Zobraz hex dáta
                            hex_data = ' '.join([f'{b:02x}' for b in data[:20]])
                            print(f"   📊 HEX: {hex_data}")
                            
                            ser.close()
                            return port.device, baudrate
                    
                    print(f"   ⚠️ Dáta bez YDLIDAR hlavičky")
                else:
                    print(f"   ❌ Žiadne dáta")
                
                ser.close()
                
            except Exception as e:
                print(f"   ❌ Chyba: {e}")
    
    print("\n❌ Nenašiel som YDLIDAR na žiadnom porte!")
    return None

def test_lidar_communication(port, baudrate):
    """Otestuj komunikáciu s LiDARom"""
    print(f"\n🧪 Testovanie komunikácie na {port} @ {baudrate}...")
    
    try:
        ser = serial.Serial(port, baudrate, timeout=1)
        time.sleep(2)
        
        packet_count = 0
        
        while packet_count < 5:
            # Hľadaj hlavičku
            found_header = False
            for _ in range(1000):
                byte1 = ser.read(1)
                if len(byte1) == 0:
                    continue
                    
                if byte1[0] == 0xAA:
                    byte2 = ser.read(1)
                    if len(byte2) > 0 and byte2[0] == 0x55:
                        found_header = True
                        break
            
            if not found_header:
                print("❌ Nenašiel som hlavičku")
                break
            
            # Čítaj hlavičku paketu
            header_data = ser.read(6)
            if len(header_data) < 6:
                continue
                
            ct = header_data[0]
            lsn = header_data[1]
            fsa = struct.unpack('<H', header_data[2:4])[0]
            lsa = struct.unpack('<H', header_data[4:6])[0]
            
            # Čítaj checksum
            cs_data = ser.read(2)
            if len(cs_data) < 2:
                continue
            cs = struct.unpack('<H', cs_data)[0]
            
            # Čítaj vzorky
            if lsn > 0 and lsn < 255:
                sample_data = ser.read(lsn * 2)
                if len(sample_data) == lsn * 2:
                    print(f"📦 Paket #{packet_count + 1}: CT={ct:02x}, LSN={lsn}, FSA={fsa}, LSA={lsa}")
                    
                    # Spracuj prvé 3 vzorky
                    valid_samples = 0
                    for i in range(min(3, lsn)):
                        distance_raw = struct.unpack('<H', sample_data[i*2:(i+1)*2])[0]
                        distance = distance_raw / 4.0  # mm
                        if distance > 0:
                            print(f"   Vzorka {i}: {distance:.1f}mm")
                            valid_samples += 1
                    
                    if valid_samples > 0:
                        packet_count += 1
                    else:
                        print("   ⚠️ Žiadne platné vzorky")
                else:
                    print(f"   ❌ Neúplné vzorky")
            else:
                print(f"   ❌ Neplatný LSN: {lsn}")
        
        ser.close()
        
        if packet_count >= 3:
            print(f"✅ Komunikácia funguje! Spracoval som {packet_count} paketov")
            return True
        else:
            print(f"❌ Komunikácia nefunguje správne (len {packet_count} paketov)")
            return False
            
    except Exception as e:
        print(f"❌ Chyba pri testovaní: {e}")
        return False

if __name__ == "__main__":
    print("🚀 YDLIDAR X4 - Hľadanie a testovanie")
    print("=" * 50)
    
    # Nájdi LiDAR
    result = find_lidar_port()
    
    if result:
        port, baudrate = result
        print(f"\n🎉 YDLIDAR nájdený na {port} @ {baudrate}")
        
        # Otestuj komunikáciu
        if test_lidar_communication(port, baudrate):
            print(f"\n✅ VÝSLEDOK: Použite port {port} s baudrate {baudrate}")
            print("🚀 Môžete spustiť hlavný program!")
        else:
            print(f"\n❌ VÝSLEDOK: Port {port} nefunguje správne")
    else:
        print("\n❌ VÝSLEDOK: YDLIDAR sa nepodarilo nájsť")
        print("💡 Skontrolujte:")
        print("   - Je LiDAR zapnutý a točí sa?")
        print("   - Je USB kábel pripojený?")
        print("   - Sú nainštalované ovládače?")
