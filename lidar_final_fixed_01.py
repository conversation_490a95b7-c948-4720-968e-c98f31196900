import serial
import struct
import math
import matplotlib.pyplot as plt
import numpy as np
import time
import threading
from collections import deque

print("🚀 YDLIDAR X4 - FINÁLNA FUNKČNÁ VERZIA")
print("🎯 Zobrazujem prekážky v rozsahu 0-2m")

# Globálne premenné
ser = None
running = False
all_points = deque(maxlen=2000)
packet_count = 0
data_lock = threading.Lock()

def send_lidar_command(ser, command):
    """Pošli príkaz LiDARu"""
    ser.write(command)
    time.sleep(0.1)

def connect_lidar():
    """Pripoj sa k LiDARu a spusti skenovanie"""
    global ser
    
    print("🔌 Pripájam sa k YDLIDAR X4 na COM4...")
    
    try:
        ser = serial.Serial('COM4', 128000, timeout=0.1)
        print("✅ Pripojený na baudrate 128000")
        time.sleep(1)
        
        # Vyčisti buffer
        ser.reset_input_buffer()
        
        # <PERSON><PERSON><PERSON> príkazy na spustenie skeningu
        print("📤 Spúšťam skenovanie...")
        
        # Príkaz na spustenie skeningu: 0xA5 0x60
        send_lidar_command(ser, b'\xA5\x60')
        
        # Príkaz na nastavenie rýchlosti: 0xA5 0xF0 0x02 0x94 0x02
        send_lidar_command(ser, b'\xA5\xF0\x02\x94\x02')
        
        time.sleep(2)  # Počkaj na spustenie
        
        # Test komunikácie
        test_data = ser.read(200)
        if len(test_data) > 0:
            print(f"✅ Prijímam dáta ({len(test_data)} bytov)")
            return True
        else:
            print("❌ Žiadne dáta - skontroluj napájanie LiDARu!")
            return False
            
    except Exception as e:
        print(f"❌ Nepodarilo sa pripojiť: {e}")
        return False

def read_lidar_thread():
    """Thread pre čítanie dát z LiDARu"""
    global running, packet_count, all_points
    
    while running:
        try:
            # Hľadaj hlavičku 0xAA55
            found_header = False
            for _ in range(100):
                byte1 = ser.read(1)
                if len(byte1) == 0:
                    continue
                    
                if byte1[0] == 0xAA:
                    byte2 = ser.read(1)
                    if len(byte2) > 0 and byte2[0] == 0x55:
                        found_header = True
                        break
            
            if not found_header:
                continue
            
            # Čítaj hlavičku paketu
            header_data = ser.read(6)
            if len(header_data) < 6:
                continue
                
            ct = header_data[0]
            lsn = header_data[1]
            fsa = struct.unpack('<H', header_data[2:4])[0]
            lsa = struct.unpack('<H', header_data[4:6])[0]
            
            # Kontrola platnosti
            if lsn == 0 or lsn > 200:
                continue
            
            # Čítaj checksum
            cs_data = ser.read(2)
            if len(cs_data) < 2:
                continue
            
            # Čítaj vzorky dát
            sample_data = ser.read(lsn * 2)
            if len(sample_data) < lsn * 2:
                continue
            
            # Spracuj dáta
            angle_fsa = (fsa >> 1) / 64.0
            angle_lsa = (lsa >> 1) / 64.0
            
            angle_diff = angle_lsa - angle_fsa
            if angle_diff < 0:
                angle_diff += 360
            
            new_points = []
            for i in range(lsn):
                # Extrahuj vzdialenosť
                distance_raw = struct.unpack('<H', sample_data[i*2:(i+1)*2])[0]
                distance = distance_raw / 4.0 / 1000.0  # Konverzia na metre
                
                # Preskač nulové vzdialenosti
                if distance == 0:
                    continue
                
                # Vypočítaj uhol pre túto vzorku
                if lsn > 1:
                    angle = i * angle_diff / (lsn - 1) + angle_fsa
                else:
                    angle = angle_fsa
                
                # Normalizuj uhol
                while angle >= 360:
                    angle -= 360
                while angle < 0:
                    angle += 360
                
                # Filtruj len body v rozsahu 0.05-2m
                if 0.05 <= distance <= 2.0:
                    angle_rad = math.radians(angle)
                    x = distance * math.cos(angle_rad)
                    y = distance * math.sin(angle_rad)
                    new_points.append((x, y, distance))
            
            # Pridaj nové body do bufferu
            if new_points:
                with data_lock:
                    all_points.extend(new_points)
                    packet_count += 1
            
        except Exception as e:
            if running:
                print(f"❌ Chyba pri čítaní: {e}")
            break

def main():
    """Hlavná funkcia"""
    global running
    
    # Pripoj sa k LiDARu
    if not connect_lidar():
        return
    
    # Spusti thread pre čítanie dát
    running = True
    read_thread = threading.Thread(target=read_lidar_thread, daemon=True)
    read_thread.start()
    
    # Nastavenie grafu
    plt.ion()
    fig, ax = plt.subplots(figsize=(12, 12))
    
    print("🎨 Spúšťam vizualizáciu...")
    print("🔴 Farby: Modrá = blízko, Červená = ďaleko")
    print("⭕ Sivé kruhy = vzdialenostné značky")
    print("❌ Zatvor okno alebo stlač Ctrl+C pre ukončenie")
    
    try:
        last_update = time.time()
        
        while True:
            current_time = time.time()
            
            # Aktualizuj graf každých 100ms
            if current_time - last_update > 0.1:
                with data_lock:
                    if all_points:
                        # Vyčisti graf
                        ax.clear()
                        
                        # Nastavenia grafu
                        ax.set_title(f"YDLIDAR X4 - Live Scan (0-2m) - {len(all_points)} bodov - Pakety: {packet_count}", 
                                   fontsize=14, fontweight='bold')
                        ax.set_xlim(-2.5, 2.5)
                        ax.set_ylim(-2.5, 2.5)
                        ax.grid(True, alpha=0.3)
                        ax.set_aspect('equal')
                        ax.set_xlabel('X [m]')
                        ax.set_ylabel('Y [m]')
                        
                        # Pridaj kruhy pre orientáciu
                        for r in [0.5, 1.0, 1.5, 2.0]:
                            circle = plt.Circle((0, 0), r, fill=False, color='gray', alpha=0.3, linewidth=1)
                            ax.add_patch(circle)
                            ax.text(r, 0.05, f'{r}m', fontsize=9, ha='center', color='gray')
                        
                        # Konvertuj na numpy array
                        points_array = np.array([(p[0], p[1]) for p in all_points])
                        distances = np.array([p[2] for p in all_points])
                        
                        # Vykresli body s farbami podľa vzdialenosti
                        colors = plt.cm.plasma(distances / 2.0)
                        ax.scatter(points_array[:, 0], points_array[:, 1], 
                                  s=3, c=colors, alpha=0.7, edgecolors='none')
                        
                        # Pridaj informácie
                        info_text = f"Pakety: {packet_count}\nBody: {len(all_points)}\nFPS: {1/(current_time-last_update):.1f}"
                        ax.text(-2.4, 2.2, info_text, fontsize=10, 
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
                
                plt.draw()
                plt.pause(0.01)
                last_update = current_time
            
            # Kontrola, či je okno stále otvorené
            if not plt.get_fignums():
                break
            
            time.sleep(0.01)
            
    except KeyboardInterrupt:
        print("\n🛑 Ukončujem program...")
    except Exception as e:
        print(f"\n❌ Chyba: {e}")
    finally:
        running = False
        if ser and ser.is_open:
            ser.close()
            print("🔌 Sériový port zatvorený")
        plt.close('all')
        print("✅ Program ukončený")

if __name__ == "__main__":
    main()
