import serial
import struct
import math
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
import time

# Inicializuj sériový port s správnym baudrate
print("🔌 Pripájam sa k YDLIDAR X4 na COM4...")

try:
    ser = serial.Serial('COM4', 128000, timeout=1)
    print("✅ Pripojený na baudrate 128000")
    time.sleep(2)  # Počkaj na inicializáciu
except Exception as e:
    print(f"❌ Nepodarilo sa pripojiť: {e}")
    print("💡 Skontroluj:")
    print("   - Je LiDAR zapnutý?")
    print("   - Je pripojený na COM4?")
    print("   - Nie je port obsadený iným programom?")
    exit(1)

# Vykresli graf
fig, ax = plt.subplots(figsize=(10, 10))
ax.set_title("YDLIDAR X4 - Live Scan (0-2m)")
ax.set_xlim(-2.5, 2.5)
ax.set_ylim(-2.5, 2.5)
ax.grid(True, alpha=0.3)
ax.set_aspect('equal')
scatter = ax.scatter([], [], s=1, c='red', alpha=0.6)

# Pridaj kruhy pre orientáciu
circles = [plt.Circle((0, 0), r, fill=False, color='gray', alpha=0.3) for r in [0.5, 1.0, 1.5, 2.0]]
for circle in circles:
    ax.add_patch(circle)

latest_points = []
packet_count = 0

def read_packet():
    global packet_count

    while True:
        try:
            # Hľadaj hlavičku 0xAA55 (pozor na správne poradie!)
            byte1 = ser.read(1)
            if len(byte1) == 0:
                continue

            if byte1[0] != 0xAA:
                continue

            byte2 = ser.read(1)
            if len(byte2) == 0 or byte2[0] != 0x55:
                continue

            # Čítaj CT (Package Type)
            ct_byte = ser.read(1)
            if len(ct_byte) == 0:
                continue
            ct = ct_byte[0]

            # Čítaj LSN (Sample Data Number)
            lsn_byte = ser.read(1)
            if len(lsn_byte) == 0:
                continue
            lsn = lsn_byte[0]

            if lsn == 0 or lsn > 200:  # Rozumný limit
                continue

            # Čítaj FSA (Starting Angle) - 2 byty
            fsa_bytes = ser.read(2)
            if len(fsa_bytes) < 2:
                continue
            fsa = struct.unpack('<H', fsa_bytes)[0]

            # Čítaj LSA (End Angle) - 2 byty
            lsa_bytes = ser.read(2)
            if len(lsa_bytes) < 2:
                continue
            lsa = struct.unpack('<H', lsa_bytes)[0]

            # Čítaj CS (Check Code) - 2 byty
            cs_bytes = ser.read(2)
            if len(cs_bytes) < 2:
                continue
            cs = struct.unpack('<H', cs_bytes)[0]

            # Čítaj vzorky dát (každá vzorka je 2 byty pre vzdialenosť)
            sample_data = ser.read(lsn * 2)
            if len(sample_data) < lsn * 2:
                continue

            # Spracuj vzdialenosti a uhly podľa oficiálneho protokolu
            points = []

            # Vypočítaj uhly
            angle_fsa = (fsa >> 1) / 64.0  # Posun o 1 bit doprava a delenie 64
            angle_lsa = (lsa >> 1) / 64.0

            angle_diff = angle_lsa - angle_fsa
            if angle_diff < 0:
                angle_diff += 360

            for i in range(lsn):
                # Extrahuj vzdialenosť (2 byty, little endian)
                distance_raw = struct.unpack('<H', sample_data[i*2:(i+1)*2])[0]
                distance = distance_raw / 4.0 / 1000.0  # Pre Triangle LiDAR: Si/4, konverzia na metre

                # Vypočítaj uhol pre túto vzorku
                if lsn > 1:
                    angle = i * angle_diff / (lsn - 1) + angle_fsa
                else:
                    angle = angle_fsa

                # Aplikuj korekciu uhla pre Triangle LiDAR (YDLIDAR X4)
                if distance > 0:
                    ang_correct = math.atan(21.8 * (155.3 - distance * 1000) / (155.3 * distance * 1000))
                    angle += ang_correct * 180 / math.pi

                # Normalizuj uhol
                if angle >= 360:
                    angle -= 360
                elif angle < 0:
                    angle += 360

                # Filtruj len body v rozsahu 0-2m
                if 0.05 <= distance <= 2.0:
                    angle_rad = math.radians(angle)
                    x = distance * math.cos(angle_rad)
                    y = distance * math.sin(angle_rad)
                    points.append((x, y))

            packet_count += 1
            if packet_count % 10 == 0:  # Zobraz info každých 10 paketov
                print(f"✅ Spracovaný paket #{packet_count}, {len(points)} platných bodov")

            if points:
                return points

        except Exception as e:
            if packet_count % 50 == 0:  # Zobraz chyby len občas
                print(f"❌ Chyba pri čítaní paketu: {e}")
            continue

def update(frame):
    global latest_points
    try:
        new_points = read_packet()
        if new_points:
            latest_points = new_points
            points_array = np.array(latest_points)
            scatter.set_offsets(points_array)
            print(f"🎨 Zobrazujem {len(latest_points)} bodov")
        else:
            scatter.set_offsets([])
    except Exception as e:
        print(f"❌ Chyba pri aktualizácii grafu: {e}")
        scatter.set_offsets([])

    return scatter,

print("🚀 Spúšťam vizualizáciu...")
print("📍 Zobrazujem prekážky v rozsahu 0-2m")
print("🔴 Červené body = detekované prekážky")
print("⭕ Sivé kruhy = vzdialenostné značky (0.5m, 1m, 1.5m, 2m)")

ani = animation.FuncAnimation(fig, update, interval=50, blit=True)
plt.show()

# Zatvor sériový port po ukončení
ser.close()
print("🔌 Sériový port zatvorený")
