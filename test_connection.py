import serial
import struct
import time

def test_connection():
    print("🔌 Testovanie pripojenia k YDLIDAR X4 na COM4...")
    
    try:
        ser = serial.Serial('COM4', 128000, timeout=2)
        print("✅ Sériový port otvorený")
        time.sleep(2)
        
        # Prečítaj nejaké dáta
        data = ser.read(100)
        if len(data) > 0:
            print(f"✅ Prijal som {len(data)} bytov")
            
            # Zobraz prvých 20 bytov
            hex_data = ' '.join([f'{b:02x}' for b in data[:20]])
            print(f"📊 HEX: {hex_data}")
            
            # Hľadaj hlavičku
            for i in range(len(data) - 1):
                if data[i] == 0xAA and data[i+1] == 0x55:
                    print(f"🎯 Našiel som hlavičku na pozícii {i}")
                    return True
            
            print("❌ Nenašiel som hlavičku 0xAA55")
            return False
        else:
            print("❌ Žiadne dáta")
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False
    finally:
        if 'ser' in locals():
            ser.close()

if __name__ == "__main__":
    if test_connection():
        print("✅ Pripojenie funguje!")
    else:
        print("❌ Pripojenie nefunguje!")
