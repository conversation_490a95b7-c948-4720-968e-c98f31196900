import serial
import struct
import math
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
import time

print("🚀 YDLIDAR X4 - Jednoduchá verzia")
print("🎯 Zobrazujem prekážky v rozsahu 0-2m")

# Inicializuj sériový port
print("🔌 Pripájam sa k YDLIDAR X4 na COM4...")
try:
    ser = serial.Serial('COM4', 128000, timeout=0.1)  # Kratš<PERSON> timeout
    print("✅ Pripojený na baudrate 128000")
    time.sleep(1)
except Exception as e:
    print(f"❌ Nepodarilo sa pripojiť: {e}")
    print("💡 Skontroluj:")
    print("   - Je LiDAR zapnutý a točí sa?")
    print("   - Je pripojený na COM4?")
    print("   - Nie je port obsadený iným programom?")
    exit(1)

# Nastavenie grafu
plt.ion()  # Interaktívny režim
fig, ax = plt.subplots(figsize=(10, 10))
ax.set_title("YDLIDAR X4 - Live Scan (0-2m)", fontsize=14, fontweight='bold')
ax.set_xlim(-2.5, 2.5)
ax.set_ylim(-2.5, 2.5)
ax.grid(True, alpha=0.3)
ax.set_aspect('equal')
ax.set_xlabel('X [m]')
ax.set_ylabel('Y [m]')

# Pridaj kruhy pre orientáciu
for r in [0.5, 1.0, 1.5, 2.0]:
    circle = plt.Circle((0, 0), r, fill=False, color='gray', alpha=0.3, linewidth=1)
    ax.add_patch(circle)
    ax.text(r, 0.05, f'{r}m', fontsize=9, ha='center', color='gray')

# Globálne premenné
all_points = []
packet_count = 0

def read_lidar_data():
    """Čítaj dáta z LiDARu"""
    global packet_count
    
    points = []
    packets_read = 0
    
    # Čítaj niekoľko paketov naraz pre plynulejší obraz
    while packets_read < 5:
        try:
            # Hľadaj hlavičku 0xAA55
            found_header = False
            for _ in range(1000):  # Max 1000 pokusov
                byte1 = ser.read(1)
                if len(byte1) == 0:
                    continue
                    
                if byte1[0] == 0xAA:
                    byte2 = ser.read(1)
                    if len(byte2) > 0 and byte2[0] == 0x55:
                        found_header = True
                        break
            
            if not found_header:
                break
            
            # Čítaj hlavičku paketu
            header_data = ser.read(6)  # CT, LSN, FSA, LSA
            if len(header_data) < 6:
                continue
                
            ct = header_data[0]
            lsn = header_data[1]
            fsa = struct.unpack('<H', header_data[2:4])[0]
            lsa = struct.unpack('<H', header_data[4:6])[0]
            
            # Kontrola platnosti
            if lsn == 0 or lsn > 200:
                continue
            
            # Čítaj checksum
            cs_data = ser.read(2)
            if len(cs_data) < 2:
                continue
            
            # Čítaj vzorky dát
            sample_data = ser.read(lsn * 2)
            if len(sample_data) < lsn * 2:
                continue
            
            # Spracuj dáta
            angle_fsa = (fsa >> 1) / 64.0
            angle_lsa = (lsa >> 1) / 64.0
            
            angle_diff = angle_lsa - angle_fsa
            if angle_diff < 0:
                angle_diff += 360
            
            for i in range(lsn):
                # Extrahuj vzdialenosť
                distance_raw = struct.unpack('<H', sample_data[i*2:(i+1)*2])[0]
                distance = distance_raw / 4.0 / 1000.0  # Konverzia na metre
                
                # Preskač nulové vzdialenosti
                if distance == 0:
                    continue
                
                # Vypočítaj uhol pre túto vzorku
                if lsn > 1:
                    angle = i * angle_diff / (lsn - 1) + angle_fsa
                else:
                    angle = angle_fsa
                
                # Normalizuj uhol
                while angle >= 360:
                    angle -= 360
                while angle < 0:
                    angle += 360
                
                # Filtruj len body v rozsahu 0.05-2m
                if 0.05 <= distance <= 2.0:
                    angle_rad = math.radians(angle)
                    x = distance * math.cos(angle_rad)
                    y = distance * math.sin(angle_rad)
                    points.append((x, y))
            
            packets_read += 1
            packet_count += 1
            
        except Exception as e:
            break
    
    return points

def update_plot():
    """Aktualizuj graf"""
    global all_points
    
    try:
        new_points = read_lidar_data()
        if new_points:
            all_points = new_points  # Nahraď staré body novými
            
            if len(all_points) > 0:
                # Vyčisti graf a znovu vykresli
                ax.clear()
                
                # Nastavenia grafu
                ax.set_title(f"YDLIDAR X4 - Live Scan (0-2m) - {len(all_points)} bodov", 
                           fontsize=14, fontweight='bold')
                ax.set_xlim(-2.5, 2.5)
                ax.set_ylim(-2.5, 2.5)
                ax.grid(True, alpha=0.3)
                ax.set_aspect('equal')
                ax.set_xlabel('X [m]')
                ax.set_ylabel('Y [m]')
                
                # Pridaj kruhy
                for r in [0.5, 1.0, 1.5, 2.0]:
                    circle = plt.Circle((0, 0), r, fill=False, color='gray', alpha=0.3, linewidth=1)
                    ax.add_patch(circle)
                    ax.text(r, 0.05, f'{r}m', fontsize=9, ha='center', color='gray')
                
                # Vykresli body
                points_array = np.array(all_points)
                distances = np.sqrt(points_array[:, 0]**2 + points_array[:, 1]**2)
                colors = plt.cm.plasma(distances / 2.0)  # Normalizuj na 0-2m
                
                ax.scatter(points_array[:, 0], points_array[:, 1], 
                          s=5, c=colors, alpha=0.8, edgecolors='none')
                
                # Pridaj informácie
                ax.text(-2.4, 2.3, f"Pakety: {packet_count}", fontsize=10, 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
                
        plt.draw()
        plt.pause(0.05)  # Krátka pauza pre aktualizáciu
        
    except Exception as e:
        print(f"❌ Chyba pri aktualizácii: {e}")

# Hlavná slučka
print("🎨 Spúšťam vizualizáciu...")
print("🔴 Farby: Modrá = blízko, Červená = ďaleko")
print("⭕ Sivé kruhy = vzdialenostné značky")
print("❌ Stlač Ctrl+C pre ukončenie")

try:
    plt.show(block=False)
    
    while True:
        update_plot()
        
        # Kontrola, či je okno stále otvorené
        if not plt.get_fignums():
            break
            
except KeyboardInterrupt:
    print("\n🛑 Ukončujem program...")
except Exception as e:
    print(f"\n❌ Chyba: {e}")
finally:
    if 'ser' in globals() and ser.is_open:
        ser.close()
        print("🔌 Sériový port zatvorený")
    plt.close('all')
    print("✅ Program ukončený")
