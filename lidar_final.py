import serial
import struct
import math
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
import time

print("🚀 YDLIDAR X4 - Finálna verzia")
print("🎯 Zobrazujem prekážky v rozsahu 0-2m")

# Inicializuj sériový port
print("🔌 Pripájam sa k YDLIDAR X4 na COM4...")
try:
    ser = serial.Serial('COM4', 128000, timeout=1)
    print("✅ Pripojený na baudrate 128000")
    time.sleep(2)
except Exception as e:
    print(f"❌ Nepodarilo sa pripojiť: {e}")
    print("💡 Skontroluj:")
    print("   - Je LiDAR zapnutý?")
    print("   - Je pripojený na COM4?")
    print("   - Nie je port obsadený iným programom?")
    exit(1)

# Nastavenie grafu
fig, ax = plt.subplots(figsize=(12, 12))
ax.set_title("YDLIDAR X4 - <PERSON>an (0-2m)", fontsize=16, fontweight='bold')
ax.set_xlim(-2.5, 2.5)
ax.set_ylim(-2.5, 2.5)
ax.grid(True, alpha=0.3)
ax.set_aspect('equal')
ax.set_xlabel('X [m]', fontsize=12)
ax.set_ylabel('Y [m]', fontsize=12)

# Pridaj kruhy pre orientáciu
circles = [plt.Circle((0, 0), r, fill=False, color='gray', alpha=0.3, linewidth=1) 
           for r in [0.5, 1.0, 1.5, 2.0]]
for circle in circles:
    ax.add_patch(circle)

# Pridaj text pre vzdialenosti
for r in [0.5, 1.0, 1.5, 2.0]:
    ax.text(r, 0.05, f'{r}m', fontsize=10, ha='center', color='gray')

# Scatter plot pre body
scatter = ax.scatter([], [], s=3, c='red', alpha=0.8, edgecolors='darkred', linewidth=0.5)

# Globálne premenné
latest_points = []
packet_count = 0
total_points = 0

def read_lidar_packet():
    """Čítaj jeden paket z LiDARu"""
    global packet_count, total_points
    
    try:
        # Hľadaj hlavičku 0xAA55
        while True:
            byte1 = ser.read(1)
            if len(byte1) == 0:
                continue
                
            if byte1[0] == 0xAA:
                byte2 = ser.read(1)
                if len(byte2) > 0 and byte2[0] == 0x55:
                    break
        
        # Čítaj hlavičku paketu
        header_data = ser.read(6)  # CT, LSN, FSA, LSA
        if len(header_data) < 6:
            return []
            
        ct = header_data[0]
        lsn = header_data[1]
        fsa = struct.unpack('<H', header_data[2:4])[0]
        lsa = struct.unpack('<H', header_data[4:6])[0]
        
        # Kontrola platnosti
        if lsn == 0 or lsn > 200:
            return []
        
        # Čítaj checksum
        cs_data = ser.read(2)
        if len(cs_data) < 2:
            return []
        cs = struct.unpack('<H', cs_data)[0]
        
        # Čítaj vzorky dát
        sample_data = ser.read(lsn * 2)
        if len(sample_data) < lsn * 2:
            return []
        
        # Spracuj dáta
        points = []
        
        # Vypočítaj uhly
        angle_fsa = (fsa >> 1) / 64.0
        angle_lsa = (lsa >> 1) / 64.0
        
        angle_diff = angle_lsa - angle_fsa
        if angle_diff < 0:
            angle_diff += 360
        
        for i in range(lsn):
            # Extrahuj vzdialenosť
            distance_raw = struct.unpack('<H', sample_data[i*2:(i+1)*2])[0]
            distance = distance_raw / 4.0 / 1000.0  # Konverzia na metre
            
            # Preskač nulové vzdialenosti
            if distance == 0:
                continue
            
            # Vypočítaj uhol pre túto vzorku
            if lsn > 1:
                angle = i * angle_diff / (lsn - 1) + angle_fsa
            else:
                angle = angle_fsa
            
            # Aplikuj korekciu uhla pre Triangle LiDAR
            if distance > 0:
                ang_correct = math.atan(21.8 * (155.3 - distance * 1000) / (155.3 * distance * 1000))
                angle += ang_correct * 180 / math.pi
            
            # Normalizuj uhol
            while angle >= 360:
                angle -= 360
            while angle < 0:
                angle += 360
            
            # Filtruj len body v rozsahu 0.05-2m
            if 0.05 <= distance <= 2.0:
                angle_rad = math.radians(angle)
                x = distance * math.cos(angle_rad)
                y = distance * math.sin(angle_rad)
                points.append((x, y))
        
        packet_count += 1
        total_points += len(points)
        
        # Zobraz info každých 50 paketov
        if packet_count % 50 == 0:
            print(f"📊 Paket #{packet_count}: {len(points)} bodov, celkom {total_points} bodov")
        
        return points
        
    except Exception as e:
        if packet_count % 100 == 0:  # Zobraz chyby len občas
            print(f"❌ Chyba: {e}")
        return []

def update_plot(frame):
    """Aktualizuj graf"""
    global latest_points
    
    try:
        new_points = read_lidar_packet()
        if new_points:
            latest_points = new_points
            points_array = np.array(latest_points)
            scatter.set_offsets(points_array)
            
            # Aktualizuj farbu podľa vzdialenosti
            distances = np.sqrt(points_array[:, 0]**2 + points_array[:, 1]**2)
            colors = plt.cm.plasma(distances / 2.0)  # Normalizuj na 0-2m
            scatter.set_color(colors)
        else:
            # Ak nie sú nové body, zachovaj staré
            pass
            
    except Exception as e:
        print(f"❌ Chyba pri aktualizácii: {e}")
    
    return scatter,

# Spusti animáciu
print("🎨 Spúšťam vizualizáciu...")
print("🔴 Farby: Modrá = blízko (0m), Žltá = stred (1m), Červená = ďaleko (2m)")
print("⭕ Sivé kruhy = vzdialenostné značky")
print("❌ Zatvor okno pre ukončenie")

ani = animation.FuncAnimation(fig, update_plot, interval=100, blit=False)

try:
    plt.show()
except KeyboardInterrupt:
    print("\n🛑 Ukončujem program...")
except Exception as e:
    print(f"\n❌ Chyba pri zobrazení: {e}")
finally:
    if 'ser' in globals() and ser.is_open:
        ser.close()
        print("🔌 Sériový port zatvorený")
